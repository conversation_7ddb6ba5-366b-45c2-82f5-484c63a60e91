import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/custom_button.dart';
import 'package:culture_connect/widgets/custom_text_field.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:intl/intl.dart';
import 'dart:io' show Platform;

enum RegistrationStep {
  personalInfo,
  contactInfo,
  security,
  termsAndConditions,
}

class RegistrationScreen extends ConsumerStatefulWidget {
  const RegistrationScreen({super.key});

  @override
  ConsumerState<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends ConsumerState<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _dobController = TextEditingController();

  String? _errorMessage;
  bool _isLoading = false;
  String _selectedCountryCode = '+234'; // Default to Nigeria
  DateTime? _selectedDate;
  bool _acceptTerms = false;
  RegistrationStep _currentStep = RegistrationStep.personalInfo;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  final PageController _pageController = PageController();

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _dobController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // Validation methods
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }
    // Check for at least one number
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }
    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'This field is required';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    if (!RegExp(r'^\d{9,15}$').hasMatch(value)) {
      return 'Enter a valid phone number';
    }
    return null;
  }

  String? _validateDob(String? value) {
    if (value == null || value.isEmpty) {
      return 'Date of birth is required';
    }
    return null;
  }

  // Date picker
  Future<void> _selectDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = DateTime(now.year - 18, now.month, now.day);
    final DateTime firstDate = DateTime(now.year - 100);
    final DateTime lastDate = DateTime(now.year - 13, now.month, now.day);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              onSurface: AppTheme.textPrimaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dobController.text = DateFormat('dd/MM/yyyy').format(picked);
      });
    }
  }

  // Country code selection
  void _showCountryCodePicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Country Code',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    _buildCountryCodeTile('+234', 'Nigeria'),
                    _buildCountryCodeTile('+233', 'Ghana'),
                    _buildCountryCodeTile('+27', 'South Africa'),
                    _buildCountryCodeTile('+254', 'Kenya'),
                    _buildCountryCodeTile('+20', 'Egypt'),
                    _buildCountryCodeTile('+251', 'Ethiopia'),
                    _buildCountryCodeTile('+212', 'Morocco'),
                    _buildCountryCodeTile('+216', 'Tunisia'),
                    _buildCountryCodeTile('+256', 'Uganda'),
                    _buildCountryCodeTile('+255', 'Tanzania'),
                    _buildCountryCodeTile('+1', 'United States'),
                    _buildCountryCodeTile('+44', 'United Kingdom'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCountryCodeTile(String code, String country) {
    return ListTile(
      title: Text(country),
      trailing: Text(
        code,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
      onTap: () {
        setState(() {
          _selectedCountryCode = code;
        });
        Navigator.pop(context);
      },
      selected: _selectedCountryCode == code,
      selectedTileColor: Colors.blue.withAlpha(20),
    );
  }

  // Registration with email and password
  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_acceptTerms) {
      setState(() {
        _errorMessage = 'You must accept the Terms and Conditions to continue';
      });
      return;
    }

    setState(() {
      _errorMessage = null;
      _isLoading = true;
    });

    try {
      // Use the auth provider to register
      await ref.read(authStateProvider.notifier).registerWithEmailAndPassword(
            email: _emailController.text.trim(),
            password: _passwordController.text,
            firstName: _firstNameController.text.trim(),
            lastName: _lastNameController.text.trim(),
            phoneNumber: '$_selectedCountryCode${_phoneController.text.trim()}',
            dateOfBirth: _dobController.text,
          );

      // Navigate to verification screen
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/verify');
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  // Social sign-in methods
  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await ref.read(authStateProvider.notifier).signInWithGoogle();
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/home');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Google sign-in failed: $e';
        _isLoading = false;
      });
    }
  }

  // Navigation between steps
  void _nextStep() {
    if (_currentStep == RegistrationStep.termsAndConditions) {
      _register();
      return;
    }

    // Validate current step before proceeding
    if (_currentStep == RegistrationStep.personalInfo) {
      if (_firstNameController.text.isEmpty ||
          _lastNameController.text.isEmpty ||
          _dobController.text.isEmpty) {
        setState(() {
          _errorMessage = 'Please fill in all required fields';
        });
        return;
      }
    } else if (_currentStep == RegistrationStep.contactInfo) {
      if (_emailController.text.isEmpty || _phoneController.text.isEmpty) {
        setState(() {
          _errorMessage = 'Please fill in all required fields';
        });
        return;
      }
      if (_validateEmail(_emailController.text) != null) {
        setState(() {
          _errorMessage = 'Please enter a valid email address';
        });
        return;
      }
      if (_validatePhone(_phoneController.text) != null) {
        setState(() {
          _errorMessage = 'Please enter a valid phone number';
        });
        return;
      }
    } else if (_currentStep == RegistrationStep.security) {
      if (_passwordController.text.isEmpty ||
          _confirmPasswordController.text.isEmpty) {
        setState(() {
          _errorMessage = 'Please fill in all required fields';
        });
        return;
      }
      if (_validatePassword(_passwordController.text) != null) {
        setState(() {
          _errorMessage = _validatePassword(_passwordController.text);
        });
        return;
      }
      if (_validateConfirmPassword(_confirmPasswordController.text) != null) {
        setState(() {
          _errorMessage = 'Passwords do not match';
        });
        return;
      }
    }

    setState(() {
      _errorMessage = null;
      _currentStep = RegistrationStep.values[_currentStep.index + 1];
    });

    _pageController.animateToPage(
      _currentStep.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousStep() {
    if (_currentStep == RegistrationStep.personalInfo) {
      Navigator.pop(context);
      return;
    }

    setState(() {
      _errorMessage = null;
      _currentStep = RegistrationStep.values[_currentStep.index - 1];
    });

    _pageController.animateToPage(
      _currentStep.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  // UI Components
  Widget _buildSocialLoginButtons() {
    return Column(
      children: [
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: Divider(
                color: AppTheme.dividerColor,
                thickness: 1,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Or sign up with',
                style: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 14,
                ),
              ),
            ),
            Expanded(
              child: Divider(
                color: AppTheme.dividerColor,
                thickness: 1,
              ),
            ),
          ],
        ),
        SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSocialButton(
              icon: FontAwesomeIcons.google,
              color: Colors.red,
              onPressed: _signInWithGoogle,
            ),
            if (Platform.isIOS)
              _buildSocialButton(
                icon: FontAwesomeIcons.apple,
                color: Colors.black,
                onPressed: () {},
              ),
            _buildSocialButton(
              icon: FontAwesomeIcons.facebook,
              color: Colors.blue.shade900,
              onPressed: () {},
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(50),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: FaIcon(
            icon,
            color: color,
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        SizedBox(height: 4),
        Container(
          width: 40,
          height: 3,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    );
  }

  // Step-specific UI components

  Widget _buildPersonalInfoStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Personal Information'),
        SizedBox(height: 24),
        CustomTextField(
          controller: _firstNameController,
          label: 'First Name',
          prefixIcon: Icons.person_outline,
          validator: _validateName,
        ),
        SizedBox(height: 16),
        CustomTextField(
          controller: _lastNameController,
          label: 'Last Name',
          prefixIcon: Icons.person_outline,
          validator: _validateName,
        ),
        SizedBox(height: 16),
        GestureDetector(
          onTap: () => _selectDate(context),
          child: AbsorbPointer(
            child: CustomTextField(
              controller: _dobController,
              label: 'Date of Birth',
              prefixIcon: Icons.calendar_today,
              validator: _validateDob,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactInfoStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Contact Information'),
        SizedBox(height: 24),
        CustomTextField(
          controller: _emailController,
          label: 'Email Address',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          validator: _validateEmail,
        ),
        SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: _showCountryCodePicker,
              child: Container(
                height: 56,
                padding: EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.dividerColor,
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    _selectedCountryCode,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 8),
            Expanded(
              child: CustomTextField(
                controller: _phoneController,
                label: 'Phone Number',
                prefixIcon: Icons.phone_outlined,
                keyboardType: TextInputType.phone,
                validator: _validatePhone,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSecurityStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Security'),
        SizedBox(height: 24),
        CustomTextField(
          controller: _passwordController,
          label: 'Password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscurePassword,
          validator: _validatePassword,
          suffix: IconButton(
            icon: Icon(
              _obscurePassword
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: AppTheme.textSecondaryColor,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
        ),
        SizedBox(height: 16),
        CustomTextField(
          controller: _confirmPasswordController,
          label: 'Confirm Password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscureConfirmPassword,
          validator: _validateConfirmPassword,
          suffix: IconButton(
            icon: Icon(
              _obscureConfirmPassword
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: AppTheme.textSecondaryColor,
            ),
            onPressed: () {
              setState(() {
                _obscureConfirmPassword = !_obscureConfirmPassword;
              });
            },
          ),
        ),
        SizedBox(height: 16),
        Text(
          'Password must contain:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        SizedBox(height: 8),
        _buildPasswordRequirement('At least 8 characters'),
        _buildPasswordRequirement('At least one uppercase letter'),
        _buildPasswordRequirement('At least one number'),
        _buildPasswordRequirement('At least one special character'),
      ],
    );
  }

  Widget _buildPasswordRequirement(String requirement) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 16,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(width: 8),
          Text(
            requirement,
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Terms & Conditions'),
        SizedBox(height: 24),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.dividerColor,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'By creating an account, you agree to our:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 16),
              _buildTermsLink('Terms of Service'),
              _buildTermsLink('Privacy Policy'),
              _buildTermsLink('Community Guidelines'),
              SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: _acceptTerms,
                    onChanged: (value) {
                      setState(() {
                        _acceptTerms = value ?? false;
                      });
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                  Expanded(
                    child: Text(
                      'I agree to the Terms of Service, Privacy Policy, and Community Guidelines',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTermsLink(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.arrow_right,
            size: 20,
            color: AppTheme.primaryColor,
          ),
          SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.primaryColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Create Account',
        showBackButton: true,
        onBackPressed: _previousStep,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value:
                    (_currentStep.index + 1) / RegistrationStep.values.length,
                backgroundColor: Colors.grey.shade200,
                color: AppTheme.primaryColor,
              ),

              // Main content
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Step indicator
                      Text(
                        'Step ${_currentStep.index + 1} of ${RegistrationStep.values.length}',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 8),

                      // Error message
                      if (_errorMessage != null) ...[
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.shade300,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 24,
                              ),
                              SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 16),
                      ],

                      // Step content
                      PageView(
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          _buildPersonalInfoStep(),
                          _buildContactInfoStep(),
                          _buildSecurityStep(),
                          _buildTermsStep(),
                        ],
                      ),

                      // Social login buttons (only on first step)
                      if (_currentStep == RegistrationStep.personalInfo)
                        _buildSocialLoginButtons(),
                    ],
                  ),
                ),
              ),

              // Bottom buttons
              Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    if (_currentStep != RegistrationStep.personalInfo) ...[
                      Expanded(
                        child: CustomButton(
                          onPressed: _previousStep,
                          text: 'Back',
                          type: ButtonType.outlined,
                        ),
                      ),
                      SizedBox(width: 16),
                    ],
                    Expanded(
                      child: CustomButton(
                        onPressed: _isLoading ? () {} : _nextStep,
                        text:
                            _currentStep == RegistrationStep.termsAndConditions
                                ? 'Create Account'
                                : 'Next',
                        isLoading: _isLoading,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
