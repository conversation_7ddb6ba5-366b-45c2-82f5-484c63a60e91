/// A dialog for sharing a travel service with various sharing options
library share_travel_service_dialog;

// Package imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';

// Project imports - Models
import 'package:culture_connect/models/travel/travel.dart';

// Project imports - Services
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A dialog for sharing a travel service
class ShareTravelServiceDialog extends ConsumerWidget {
  /// The travel service to share
  final TravelService travelService;

  /// Creates a new share travel service dialog
  const ShareTravelServiceDialog({
    super.key,
    required this.travelService,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 500,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Share ${_getTravelServiceTypeName(travelService)}',
                    style: theme.textTheme.titleLarge,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Travel service preview
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image
                  ClipRRect(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(8)),
                    child: Image.network(
                      travelService.imageUrl,
                      height: 150,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 150,
                          color: theme.colorScheme.surfaceContainerHighest,
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Details
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          travelService.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          travelService.location,
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${travelService.rating.toStringAsFixed(1)} (${travelService.reviewCount})',
                              style: theme.textTheme.bodyMedium,
                            ),
                            const Spacer(),
                            Text(
                              travelService.formattedPrice,
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Custom message
            TextField(
              decoration: InputDecoration(
                labelText: 'Add a message (optional)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                hintText:
                    'Check out this amazing ${_getTravelServiceTypeName(travelService).toLowerCase()}!',
              ),
              maxLines: 3,
              onChanged: (value) {
                // Store the custom message
              },
            ),

            const SizedBox(height: 24),

            // Share options
            Text(
              'Share via',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareOption(
                  context,
                  ref,
                  Icons.message,
                  'Message',
                  'message',
                ),
                _buildShareOption(
                  context,
                  ref,
                  Icons.email,
                  'Email',
                  'email',
                ),
                _buildShareOption(
                  context,
                  ref,
                  Icons.facebook,
                  'Facebook',
                  'facebook',
                ),
                _buildShareOption(
                  context,
                  ref,
                  Icons.link,
                  'Copy Link',
                  'copy_link',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a share option button
  ///
  /// [context] The build context
  /// [ref] The widget reference
  /// [icon] The icon to display
  /// [label] The label to display
  /// [method] The sharing method to use
  Widget _buildShareOption(
    BuildContext context,
    WidgetRef ref,
    IconData icon,
    String label,
    String method,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: () => _shareVia(context, ref, method),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.all(8),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  /// Shares the travel service via the specified method
  ///
  /// [context] The build context
  /// [ref] The widget reference
  /// [method] The sharing method to use
  Future<void> _shareVia(
    BuildContext context,
    WidgetRef ref,
    String method,
  ) async {
    // Get services
    final loggingService = LoggingService();
    final errorHandlingService = ErrorHandlingService(loggingService);

    try {
      // Log the share method
      loggingService.info(
        'ShareTravelServiceDialog',
        'Sharing travel service via $method: ${travelService.id}',
        {
          'travel_service_id': travelService.id,
          'travel_service_name': travelService.name,
          'travel_service_type': _getTravelServiceTypeName(travelService),
          'method': method,
        },
      );

      // Close the dialog
      Navigator.of(context).pop();

      // Share the travel service
      final shareText = _createShareText();
      final shareSubject =
          'Check out this ${_getTravelServiceTypeName(travelService).toLowerCase()}: ${travelService.name}';

      if (method == 'copy_link') {
        // Generate a shareable link (in a real app, this would be a deep link)
        final shareableLink =
            'https://cultureconnect.app/travel/${travelService.id}';

        // Copy to clipboard
        await Clipboard.setData(ClipboardData(text: shareableLink));

        // Show confirmation
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Link copied to clipboard'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        await Share.share(
          shareText,
          subject: shareSubject,
        );
      }
    } catch (e, stackTrace) {
      // Log the error
      loggingService.error(
        'ShareTravelServiceDialog',
        'Failed to share travel service',
        e,
        stackTrace,
      );

      // Handle the error using the error handling service
      errorHandlingService.handleError(
        error: e,
        context: 'ShareTravelServiceDialog._shareVia',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
        additionalData: {
          'travel_service_id': travelService.id,
          'method': method,
        },
      );

      // Show error message if context is still valid
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share. Please try again.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Creates a formatted text for sharing
  ///
  /// Returns a nicely formatted text with emojis and details about the travel service
  String _createShareText() {
    // Truncate description if it's too long
    final description = travelService.description.length > 100
        ? '${travelService.description.substring(0, 97)}...'
        : travelService.description;

    // Create a formatted share text with emojis
    return '''
🌟 Check out this amazing ${_getTravelServiceTypeName(travelService).toLowerCase()} on CultureConnect!

🏨 ${travelService.name}
📍 ${travelService.location}
⭐ ${travelServiceating.toStringAsFixed(1)}/5 (${travelServiceeviewCount} reviews)
💰 ${travelService.formattedPrice}

$description

Download CultureConnect to discover authentic cultural experiences and travel services!
''';
  }

  /// Gets a human-readable name for the travel service type
  ///
  /// [travelService] The travel service to get the type name for
  /// Returns a user-friendly name for the travel service type
  String _getTravelServiceTypeName(TravelService travelService) {
    if (travelService is CarRental) {
      return 'Car Rental';
    } else if (travelService is PrivateSecurity) {
      return 'Security Service';
    } else if (travelService is Hotel) {
      return 'Hotel';
    } else if (travelService is Restaurant) {
      return 'Restaurant';
    } else if (travelService is Flight) {
      return 'Flight';
    } else if (travelService is Cruise) {
      return 'Cruise';
    } else {
      return 'Travel Service';
    }
  }
}
