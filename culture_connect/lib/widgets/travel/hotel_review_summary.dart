import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/hotel_review.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';

/// A widget for displaying a summary of hotel reviews
class HotelReviewSummary extends StatelessWidget {
  /// The reviews to summarize
  final List<HotelReview> reviews;

  /// Creates a new hotel review summary
  const HotelReviewSummary({
    super.key,
    required this.reviews,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Calculate average overall rating
    final averageRating = _calculateAverageRating();

    // Calculate rating distribution
    final ratingDistribution = _calculateRatingDistribution();

    // Calculate category ratings
    final categoryRatings = _calculateCategoryRatings();

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Overall rating
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Rating score
                Text(
                  averageRating.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 16),

                // Rating details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RatingDisplay(
                        rating: averageRating,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getRatingText(averageRating),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Based on ${reviews.length} reviews',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Rating distribution
            Text(
              'Rating Distribution',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildRatingDistribution(ratingDistribution, theme),

            const SizedBox(height: 24),

            // Category ratings
            Text(
              'Category Ratings',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildCategoryRatings(categoryRatings, theme),
          ],
        ),
      ),
    );
  }

  double _calculateAverageRating() {
    if (reviews.isEmpty) return 0;

    final sum =
        reviews.fold<double>(0, (sum, review) => sum + review.overallRating);
    return sum / reviews.length;
  }

  Map<int, int> _calculateRatingDistribution() {
    final distribution = <int, int>{
      5: 0,
      4: 0,
      3: 0,
      2: 0,
      1: 0,
    };

    for (final review in reviews) {
      final rating = review.overallRating.round();
      if (rating >= 1 && rating <= 5) {
        distribution[rating] = (distribution[rating] ?? 0) + 1;
      }
    }

    return distribution;
  }

  Map<HotelReviewCategory, double> _calculateCategoryRatings() {
    final categoryRatings = <HotelReviewCategory, double>{};
    final categoryCounts = <HotelReviewCategory, int>{};

    for (final review in reviews) {
      for (final entry in review.categoryRatings.entries) {
        final category = entry.key;
        final rating = entry.value;

        categoryRatings[category] = (categoryRatings[category] ?? 0) + rating;
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }

    // Calculate averages
    for (final category in categoryRatings.keys.toList()) {
      final count = categoryCounts[category] ?? 0;
      if (count > 0) {
        categoryRatings[category] = categoryRatings[category]! / count;
      } else {
        categoryRatings.remove(category);
      }
    }

    return categoryRatings;
  }

  String _getRatingText(double rating) {
    if (rating >= 4.5) return 'Excellent';
    if (rating >= 4.0) return 'Very Good';
    if (rating >= 3.0) return 'Good';
    if (rating >= 2.0) return 'Fair';
    return 'Poor';
  }

  Widget _buildRatingDistribution(Map<int, int> distribution, ThemeData theme) {
    // Calculate total reviews for percentage
    final totalReviews =
        distribution.values.fold<int>(0, (sum, count) => sum + count);

    return Column(
      children: [
        for (int i = 5; i >= 1; i--)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                // Rating label
                SizedBox(
                  width: 24,
                  child: Text(
                    '$i',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Progress bar
                Expanded(
                  child: LinearProgressIndicator(
                    value: totalReviews > 0
                        ? (distribution[i] ?? 0) / totalReviews
                        : 0,
                    backgroundColor: theme.colorScheme.surfaceContainerHighest,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        _getRatingColor(i, theme)),
                    minHeight: 8,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),

                // Count and percentage
                SizedBox(
                  width: 80,
                  child: Text(
                    totalReviews > 0
                        ? '${distribution[i] ?? 0} (${((distribution[i] ?? 0) / totalReviews * 100).round()}%)'
                        : '0 (0%)',
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCategoryRatings(
      Map<HotelReviewCategory, double> categoryRatings, ThemeData theme) {
    // Sort categories by rating (highest first)
    final sortedCategories = categoryRatings.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: sortedCategories.map((entry) {
        return SizedBox(
          width: 140,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                entry.key.displayName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  RatingDisplay(
                    rating: entry.value,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Text(
                    entry.value.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Color _getRatingColor(int rating, ThemeData theme) {
    switch (rating) {
      case 5:
        return Colors.green;
      case 4:
        return Colors.lightGreen;
      case 3:
        return Colors.amber;
      case 2:
        return Colors.orange;
      case 1:
        return Colors.red;
      default:
        return theme.colorScheme.primary;
    }
  }
}
