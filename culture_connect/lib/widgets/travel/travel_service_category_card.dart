import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';

/// A card for displaying a travel service category
class TravelServiceCategoryCard extends StatelessWidget {
  /// The travel service type
  final TravelServiceType type;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new travel service category card
  const TravelServiceCategoryCard({
    super.key,
    required this.type,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                type.color.withAlpha(179),
                type.color,
              ],
            ),
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: Opacity(
                  opacity: 0.1,
                  child: CustomPaint(
                    painter: <PERSON><PERSON><PERSON><PERSON><PERSON>(
                      icon: type.icon,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              // Content
              Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Icon
                    Icon(
                      type.icon,
                      color: Colors.white,
                      size: 48,
                    ),
                    SizedBox(height: 16),

                    // Name
                    Text(
                      type.displayName,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// A painter for drawing a pattern of icons
class PatternPainter extends CustomPainter {
  /// The icon to draw
  final IconData icon;

  /// The color of the icon
  final Color color;

  /// Creates a new pattern painter
  PatternPainter({
    required this.icon,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final iconSize = size.width / 5;
    final spacing = iconSize * 1.5;

    for (var x = -spacing; x < size.width + spacing; x += spacing) {
      for (var y = -spacing; y < size.height + spacing; y += spacing) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: String.fromCharCode(icon.codePoint),
            style: TextStyle(
              fontSize: iconSize,
              fontFamily: icon.fontFamily,
              color: color,
            ),
          ),
          textDirection: TextDirection.ltr,
        );

        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x, y),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
